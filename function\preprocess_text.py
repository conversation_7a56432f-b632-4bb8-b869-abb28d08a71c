import re

from function.keyword import abbreviation_dict

def expand_abbreviations(text, abbreviation_map):
    text = _remove_punctuation(text)
    words = text.split()
    normalized_words = [abbreviation_map.get(word, word) for word in words]
    return ' '.join(normalized_words)

def _remove_punctuation(text):
    text = re.sub(r'[^\w\s]', '', text)
    return text

if __name__ == "__main__":
    input_text = "<PERSON>ình quên mkhau đã lấy lại dc, nhưng khi đang nhập lại báo sai."
    normalized_text = expand_abbreviations(input_text, abbreviation_dict)
    print("Trước:", input_text)
    print("Sau  :", normalized_text)