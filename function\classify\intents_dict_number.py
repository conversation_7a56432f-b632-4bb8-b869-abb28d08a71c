intents_dict = {
    "intents": [
        "intent_mo_the_tin_dung",
        "intent_quen_mat_khau",
        "intent_vay_tieu_dung",
        "intent_chi_nhanh_gan_nhat",
        "intent_tuyen_dung",
        "intent_khoa_the",
        "intent_sinh_loi_tu_dong",
        "intent_rut_tien_sinh_loi_tu_dong",
        "intent_doi_diem",
        "intent_tinh_diem_giao_dich",
        "intent_han_muc_chuyen_tien",
        "intent_bieu_phi_the_&_sms_banking",
        "intent_cap_nhat_sdt_email",
        "intent_chon_stk",
        "intent_giao_dich_khong_thanh_cong_&_loi_app",
        "intent_dong_tai_khoan",
        "intent_lai_suat_tiet_kiem",
        "intent_lam_the_&_tai_khoan_online",
        "intent_phi_duy_tri_tai_khoan",
        "intent_quen_ma_pin",
        "intent_sao_ke_&_kiem_tra_so_du",
        "intent_loa_chuyen_tien",
        "intent_mo_tai_khoan_doanh_nghiep",
        "intent_lua_dao",
        "intent_thoi_gian_hoat_dong",
        "intent_dang_ky_tai_khoan_duoi_18",
        "intent_thank_you",
        "intent_ask_how_to_do_something",
        "intent_feedback_negative",
        "intent_mood_unhappy",
        "intent_ask_what_can_do",
        "intent_hoi_soft_pos",
        "intent_out_of_scope",
        "intent_chaohoi",
        "intent_dong_the_tin_dung",
        "intent_is_phone_techcombank",
        "intent_cung_cap_thong_tin_ca_nhan",
        "intent_cung_cap_ma_loi",
        "intent_hoi_thong_tin_khac",
        "intent_doi_the_tu_sang_chip",
        "intent_mo_tai_khoan_tiet_kiem_online",
        "intent_thoi_gian_nhan_the",
        "intent_gia_han_the",
        "intent_hoi_the_visa_eco",
        "intent_hoi_chung_khoan_techcombank",
        "intent_hoi_dich_vu_doanh_nghiep",
        "intent_loi_ung_dung_app",
        "intent_loi_tai_khoan_giao_dich",
        "intent_yeu_cau_ho_tro_chung",
        "intent_chuyen_tien_nham",
        "intent_hoi_so_hotline_ngan_hang"
    ],
    "responses": {
        "utter_intent_mo_the_tin_dung": [
            {"text": "Ngân hàng Techcombank sẽ gửi thông báo qua ứng dụng Techcombank mobile cho các khách hàng đủ điều kiện và được phê duyệt trước hạn mức thẻ tín dụng. Sau khi nhận được thông báo quý khách có thể thao tác đăng ký phát hành thẻ trên ứng dụng TCB Mobile.\nTrường hợp quý khách chưa nhận được thông báo qua app hoặc đã nhận được thông báo nhưng muốn phát hành thẻ với hạn mức cao hơn, vui lòng liên hệ chi nhánh Techcombank để được hỗ trợ làm thủ tục thẩm định hạn mức tín dụng. Quý khách vui lòng tìm chi nhánh gần nhất tại đây nhé: https://techcombank.com/lien-he"}
        ],
        "utter_intent_quen_mat_khau": [
            {"text": "Quý khách có thể sử dụng tính năng Quên mật khẩu trên ứng dụng Techcombank mobile để tạo mới mật khẩu đăng nhập bằng cách cung cấp thông tin CMND/CCCD, số thẻ, mã PIN thẻ và mã xác thực gửi về số điện thoại đã đăng ký tài khoản.\nTrường hợp Quý khách chưa có thẻ, Quý khách vui lòng gọi Hotline miễn phí 1800 588 822 (trong nước) hoặc +84 24 3944 6699 (quốc tế) hoặc email về <EMAIL> để được hỗ trợ kịp thời."}
        ],
        "utter_intent_vay_tieu_dung": [
            {"text": "Ngân hàng có 7 hình thức vay tiêu dùng bao gồm:\n- Ứng tiền MyCash\n- Vay hạn mức thấu chi không tài sản đảm bảo\n- Vay tiêu dùng tín chấp trả góp\n- Vay tiêu dùng thế chấp linh hoạt\n- Vay hạn mức thấu chi thế chấp bằng tài sản đảm bảo\n- Vay cầm cố tiền gửi\n- Vay xây sửa nhà\nĐể tìm hiểu thêm về từng hình thức và đăng ký nhận tư vấn, quý khách vui lòng truy cập tại đây ạ https://techcombank.com/khach-hang-ca-nhan/vay/vay-tieu-dung"}
        ],
        "utter_intent_chi_nhanh_gan_nhat": [
            {"text": "Quý khách có thể tra cứu danh sách CN/PGD Techcombank tại đây: https://techcombank.com/lien-he."}
        ],
        "utter_intent_tuyen_dung": [
            {"text": "Chào bạn, bạn tham khảo thông tin tuyển dụng tại đây nhé: https://www.techcombankjobs.com/"}
        ],
        "utter_intent_khoa_the": [
            {"text": "Để khóa tạm thời, quý khách có thể tiến hành theo 2 cách: \n- Khách hàng đăng nhập ứng dụng Techcombank mobile thực hiện thao tác khóa thẻ tạm thời\n- Khách hàng liên hệ call center/Chi nhánh Techcombank làm yêu cầu khóa thẻ tạm thời \nKhi khóa thẻ tam thời, thẻ vẫn còn trên hệ thống, vẫn phát sinh phí thường niên. Khách hàng có thể yêu cầu mở khóa lại để sử dụng khi có nhu cầu.\nNếu quý khách có nhu cầu khóa vĩnh viễn (hủy thẻ vĩnh viễn), xin lưu ý thẻ sẽ bị hủy vĩnh viễn, không mở khóa lại được. Khách hàng liên hệ Call Center/Chi nhánh gần nhất làm yêu cầu hủy thẻ vĩnh viễn."}
        ],
        "utter_intent_sinh_loi_tu_dong": [
            {"text": "Sinh lời tự động là 1 tính năng của Tài khoản thanh toán. Sau khi đăng ký tính năng, nguồn tiền chưa sử dụng/ tiền nhàn rỗi trong tài khoản của quý khách sẽ được tự động chuyển sang ngăn số dư sinh lời để nhận lợi suất cao hơn. Quý khách sẽ cài đặt ngưỡng số dư cho tài khoản thanh toán, khi số dư trong tài khoản vượt ngưỡng, hệ thống sẽ tự động chuyển phần tiền vượt ngưỡng sang ngăn sinh lời. Quý khách vui lòng tham khảo chi tiết thêm tại đây: https://techcombank.com/khach-hang-ca-nhan/chi-tieu/tai-khoan/tai-khoan-thanh-toan-techcombank/sinh-loi-tu-dong"}
        ],
        "utter_intent_rut_tien_sinh_loi_tu_dong": [
            {"text": "Toàn bộ số dư vẫn luôn sẵn sàng sử dụng khi quý khách có nhu cầu. Nếu số tiền giao dịch lớn hơn số dư tài khoản thanh toán (và không lớn hơn tổng số dư tài khoản bao gồm ngăn sinh lợi), hệ thống cũng sẽ tự động dùng tiền từ ngăn số dư sinh lời để bù vào phần còn thiếu để giao dịch không bị gián đoạn."}
        ],
        "utter_intent_doi_diem": [
            {"text": "Quý khách vui lòng thực hiện theo hướng dẫn sau để đổi điểm trong chương trình Techcombank Rewards:\n- Bước 1: Chọn Techcombank Rewards tại trang chủ Techcombank Mobile.\n- Bước 2: Xem các ưu đãi tại \"Dành cho bạn\" hoặc \"Ưu đãi đặc biệt\".\n- Bước 3: Lựa chọn ưu đãi yêu thích để đổi điểm"}
        ],
        "utter_intent_tính_diem_giao_dich": [
            {"text": "Quý khách vui lòng tham khảo nguyên tắc tích điểm của Chương trình Techcombank Rewards tại đây:  https://techcombank.com/khach-hang-ca-nhan/ngan-hang-truc-tuyen/techcombank-rewards"}
        ],
        "utter_intent_han_muc_chuyen_tien": [
            {"text": "Chào bạn, nếu tài khoản của bạn đã mở tại quầy/ chi nhánh, bạn có thể kiểm tra/ thay đổi hạn mức bằng cách Đăng nhập vào ứng dụng, tại Menu cài đặt chọn \"Quản lý tài khoản\" rồi chọn \"Hạn mức giao dịch\"\n\nNếu bạn đăng ký tài khoản online và chưa định danh tại chi nhánh, hạn mức chuyển tiền tối đa là 100tr/tháng (nếu ko phát hành thẻ debit) và 60tr/tháng (nếu đã phát hành thẻ debit). Bạn vui lòng đến chi nhánh Techcombank gần nhất để nâng cấp sử dụng dịch vụ đầy đủ. Sau khi nâng cấp tài khoản, bạn có thể giao dịch với hạn mức 5 tỷ đồng/ ngày và chủ động thay đổi hạn mức giao dịch trên ứng dụng của Techcombank bạn nhé!\n\nBạn có thể tra cứu danh sách CN/PGD Techcombank tại đây: https://techcombank.com/lien-he"}
        ],
        "utter_intent_bieu_phi_the_&_sms_banking": [
            {"text": "Chào bạn, bạn tham khảo biểu phí tại đây nhé: https://techcombank.com/cong-cu-tien-ich/bieu-phi-bieu-mau-dich-vu\nĐể được tư vấn thêm chi tiết bạn vui lòng gọi Hotline miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) hoặc email về <EMAIL> để được hỗ trợ kịp thời nhé!"}
        ],
        "utter_intent_cap_nhat_sdt_email": [
            {"text": "Để thay đổi số điện thoại/email đăng ký với ngân hàng bạn vui lòng đến quầy giao dịch Techcombank gần nhất để làm thủ tục. Khi đi mang theo CMTND/CCCD còn hiệu lực.\nĐể được tư vấn thêm chi tiết bạn vui lòng gọi Hotline miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) hoặc email về <EMAIL> để được hỗ trợ kịp thời nhé!"}
        ],
        "utter_intent_chon_stk": [
            {"text": "Chào bạn, để có thể đăng ký tài khoản/ tài khoản số đẹp ngay trên ứng dụng Techcombank Mobile. bạn vui lòng chuẩn bị CMND hoặc CCCD và truy cập app Techcombank Mobile có thể tải từ CHplay hoặc Appstore và làm theo hướng dẫn sau nhé!\nhttps://techcombank.com/khach-hang-ca-nhan/chi-tieu/tai-khoan/tai-khoan-thanh-toan/tai-khoan-so-dep"}
        ],
        "utter_intent_giao_dich_khong_thanh_cong_&_loi_app": [
            {"text": "Bạn vui lòng cung cấp câu thông báo lỗi/mã lỗi chúng tôi sẽ kiểm tra và phàn hồi.\nTrường hợp đã có thông báo trừ tiền bạn vui lòng thông báo người nhận kiểm tra số dư tài khoản và liên hệ Hotline miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) để được hỗ trợ kịp thời nhé!"}
        ],
        "utter_intent_dong_tai_khoan": [
            {"text": "Chào bạn, \nNếu bạn có nhu cầu \"đóng tài khoản vĩnh viễn\":\n- Trường hợp bạn có từ 2 tài khoản đang sử dụng, muốn đóng 1 tài khoản. Bạn có thể liên hệ yêu cầu đóng 1 tài khoản không có nhu cầu sử dụng bằng cách liên hệ chi nhánh Techcombank gần nhất hoặc gọi Hotline miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) để được hỗ trợ kịp thời. \n- Trường hợp bạn chỉ có 1 tài khoản, muốn đóng luôn TK này. Bạn vui lòng đến chi nhánh Techcombank gần nhất để làm yêu cầu đóng tài khoản.\n\nNếu bạn muốn \"Hủy thẻ và vẫn dùng tài khoản\" hoặc muốn \"Khóa tài khoản tạm thời\"; bạn vui lòng liên hệ Hotline miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) để được hỗ trợ kịp thời. "}
        ],
        "utter_intent_lai_suat_tiet_kiem": [
            {"text": "Chào bạn, bạn tham khảo lãi suất tại đây nhé: https://techcombank.com/cong-cu-tien-ich/bieu-phi-lai-suat"}
        ],
        "utter_intent_lam_the_&_tai_khoan_online": [
            {"text": "Chào bạn, nếu bạn chưa có tài khoản tại Techcombank, để có thể đăng ký thẻ ATM/ thẻ Visa debit Techcombank online, bạn vui lòng chuẩn bị CMND hoặc CCCD và truy cập app Techcombank Mobile có thể tải từ CHplay hoặc Appstore và làm theo hướng dẫn sau nhé: https://youtu.be/ZI0EQhwLonU\n\nTrường hợp bạn đã có tài khoản nhưng chưa có thẻ, bạn vui lòng vào app Techcombank Mobile, chọn Tài khoản & Thẻ, chọn Tài khoản thanh toán, chọn Mở thẻ và làm theo hướng dẫn nhé."}
        ],
        "utter_intent_phi_duy_tri_tai_khoan": [
            {"text": "Techcombank miễn phí duy trì dịch vụ ngân hàng điện tử và miễn phí chuyển tiền qua ứng dụng Techcombank Mobile/Website online banking. Bên cạnh đó, bạn tham khảo biểu phí các dịch vụ khác tại đây nhé: https://techcombank.com/cong-cu-tien-ich/bieu-phi-bieu-mau-dich-vu"}
        ],
        "utter_intent_quen_ma_pin": [
            {"text": "Trường hợp này bạn vui lòng đăng nhập ứng dụng Techcombank Mobile, chọn \"tài khoản & thẻ\", chọn thẻ cần cấp lại pin , chọn \"quản lý thẻ\", chọn \"thiết lập pin\" và làm theo hướng dẫn trên ứng dụng để đặt lại mã pin mới."}
        ],
        "utter_intent_sao_ke_&_kiem_tra_so_du": [
            {"text": "Để thực hiện sao kê tài khoản ngân hàng online qua Mobile Banking, bạn hãy thực hiện các bước sau đây:\n- Bước 1: Mở ứng dụng Techcombank Mobile, Chọn “Đăng nhập” > Nhập tên đăng nhập và mật khẩu/Mã mở khoá.\n- Bước 2: Chọn “Tài khoản & Thẻ” > Chọn “Tài khoản thanh toán” bạn muốn sao kê hoặc kiểm tra lịch sử giao dịch.\n- Bước 3: Chọn Biểu tượng hình kính lúp cạnh Lịch sử giao dịch > Tìm giao dịch bằng cách lựa chọn “Lọc theo ngày”, “Lọc theo số tiền” hoặc tìm “Số tài khoản” > Chọn “Tìm kiếm”.\n- Bước 4: Kiểm tra các giao dịch được hiện trên màn hình.\n\nLưu ý : trường hợp KH cần sao kê bản cứng có dấu xác nhận vui lòng làm thủ tục tại chi nhánh Techcombank."}
        ],
        "utter_intent_loa_chuyen_tien": [
            {"text": "Bạn vui lòng để lại thông tin đăng ký Loa thanh toán cho cửa hàng tại đây:\n- Khách hàng có tài khoản Techcombank: https://techcombank.com/lp/dang-ky-soundbox-khach-hang-hien-huu\n- Khách hàng chưa có tài khoản Techcombank: https://techcombank.com/lp/dang-ky-soundbox-khach-hang-moi"}
        ],
        "utter_intent_27": [
            {"text": "Dạ để được tư vấn và hỗ trợ, bạn vui lòng liên hệ trung tâm Chăm sóc Khách hàng Doanh nghiệp 1800-6556 (trong nước) hoặc +84-24 7303 6556 (quốc tế), Hoặc liên hệ qua hòm thư: <EMAIL> nhé!"}
        ],
        "utter_intent_mo_tai_khoan_doanh_nghiep": [
            {"text": "Bạn mở tài khoản doanh nghiệp tại đây nhé: https://techcombank.com/khach-hang-doanh-nghiep/mo-tai-khoan"}
        ],
        "utter_intent_lua_dao": [
            {"text": "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn phản hồi bạn sớm ạ."}
        ],
        "utter_intent_thoi_gian_hoat_dong": [
            {"text": "Xin chào Quý khách! Ngân hàng sẽ làm việc từ 8h sáng đến 17h chiều (từ Thứ 2 đến thứ 6), riêng thứ 7 NH sẽ làm việc từ 8h sáng đến 12h trưa, Chủ nhật và ngày lễ nghỉ"}
        ],
        "utter_intent_dang_ky_tai_khoan_duoi_18": [
            {"text": "Chào bạn, đăng kí tài khoản với phương thức online định danh điện tử eKYC áp dụng cho công dân Việt Nam từ 18 tuổi trở lên;\nVới phương thức thông thường tại CN/PGD áp dụng cho tất cả các khách hàng từ 15 tuổi trở lên nhé!"}
        ],
        "utter_intent_thank_you": [
            {"text": "Rất vui khi nghe điều đó! Techcombank luôn cố gắng mang đến trải nghiệm tốt nhất cho bạn."}
        ],
        "utter_intent_mood_unhappy": [
            {"text": "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn phản hồi bạn sớm ạ."}
        ],
        "utter_intent_feedback_negative": [
            {"text": "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn cho bạn sớm ạ."}
        ],
        "utter_intent_ask_how_to_do_something": [
            {"text": "Bạn có thể cho tôi biết cụ thể bạn muốn thực hiện thao tác nào hoặc cần hướng dẫn về vấn đề gì không ạ?"}
        ],
        "utter_intent_ask_what_can_do": [
            {"text": "Tôi có thể hỗ trợ bạn các thông tin về sản phẩm, dịch vụ của Techcombank, hướng dẫn thực hiện các giao dịch, giải đáp thắc mắc và nhiều hơn nữa. Bạn cần tôi giúp gì cụ thể ạ?"}
        ],
        "utter_intent_out_of_scope": [
            {"text": "Rất tiếc, vấn đề này nằm ngoài phạm vi hỗ trợ của tôi. Bạn có cần tôi giúp gì khác liên quan đến dịch vụ ngân hàng Techcombank không?"}
        ],
        "utter_intent_hoi_soft_pos": [
            {"text": "Bạn vui lòng liên hệ hotline 1800 588 822 822 (Khách hàng Cá nhân) hoặc 1800 6556 (Khách hàng Doanh nghiệp) để được tư vấn và hỗ trợ đăng ký SoftPOS."}
        ],
        "utter_intent_chaohoi": [
            {"text": "Xin chào bạn, tôi có thể hỗ trợ bạn các thông tin về sản phẩm và dịch vụ của Techcombank."}
        ],
        "utter_intent_dong_the_tin_dung": [
            {"text": "Quý khách vui lòng gọi vào tổng đài miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) để được hỗ trợ hủy thẻ nhé!"}
        ],
        "utter_intent_is_phone_techcombank": [
            {"text": "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn cho bạn sớm ạ."}
        ],
        "utter_intent_cung_cap_thong_tin_ca_nhan": [
            {"text": "Dạ, cảm ơn anh/chị đã cung cấp thông tin. Ngân hàng sẽ xử lý yêu cầu của anh/chị trong thời gian sớm nhất. Anh/chị có cần hỗ trợ gì thêm không ạ?"}
        ],
        "utter_intent_cung_cap_ma_loi": [
            {"text": "Dạ, cảm ơn anh/chị đã cung cấp thông tin. Ngân hàng sẽ xử lý yêu cầu của anh/chị trong thời gian sớm nhất. Anh/chị có cần hỗ trợ gì thêm không ạ?"}
        ],
        "utter_intent_hoi_thong_tin_khac": [
            {"text": "Xin chào bạn, tôi có thể hỗ trợ bạn các thông tin về sản phẩm và dịch vụ của Techcombank."}
        ],
        "utter_chaohoi": [
            {"text": "Xin chào bạn, tôi có thể hỗ trợ bạn các thông tin về sản phẩm và dịch vụ của Techcombank."}
        ],
        "utter_intent_doi_the_tu_sang_chip": [
            {"text": "Dạ để chuyển đổi miễn phí thẻ từ sang thẻ chip, quý khách vui lòng làm theo hướng dẫn:\n Bước 1: Đăng nhập vào ứng dụng Techcombank, Tại mục thẻ và tài khoản, chọn tài khoản gắn với thẻ từ cần chuyển đổi, nhấn Đăng ký.\nBước 2: Chọn Nâng cấp bên dưới thẻ muốn chuyển đổi. Chọn chi tiết để xem thêm thông tin thẻ.\nBước 3: Nhập thông tin số điện thoại và địa chỉ nhận thẻ để hoàn tất đăng ký. Ứng dụng sẽ cập nhật chi tiết về tình trạng thẻ của bạn. Thẻ mới sẽ được gửi về địa chỉ đã đăng ký. "}
        ],
        "utter_intent_mo_tai_khoan_tiet_kiem_online": [
            {"text": "Thưa Quý khách, Quý khách có thể mở tài khoản tiết kiệm online theo các bước sau: \n1. Đăng nhập vào ứng dụng và nhấn chọn Tài Khoản và Thẻ \n2. Chọn Tiết kiệm & Đầu tư \n3. Chọn tiền gửi\n4. KH có thể xem Bảng lãi suất & Chọn Bắt đầu ngay để mở tài khoản \n5. Nhập số tiền. Chọn tài khoản trích tiền Sau đó chọn “Tiếp tục”. \n6. Chọn kỳ hạn muốn gửi \n7. Chọn hình thức tất toán khi hết kỳ hạn \n8. Kiểm tra lại thông tin, sau đó chọn “Tiếp tục”. Nếu muốn điều chỉnh thông tin, có thể chạm tay vào các ký tự màu xanh để sửa ngay. \n9. Chọn Xác nhận để hoàn tất mở tài khoản tiền gửi có kỳ hạn ạ"}
        ],
        "utter_intent_thoi_gian_nhan_the": [
            {"text": "Thẻ vật lý sẽ được phát hành trong vòng 3 đến 7 ngày làm việc (không bao gồm thứ Bảy và Chủ nhật) ạ. Thẻ sẽ được gửi đến địa chỉ mà bạn đã đăng ký khi đăng ký mở thẻ vật lý. Bạn có thể theo dõi trạng thái phát hành thẻ trong ứng dụng Techcombank Mobile."}
        ],
        "utter_intent_gia_han_the": [
            {"text": "Dạ để gia hạn thẻ, bạn vui lòng gọi Hotline miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) hoặc gửi yêu cầu về hòm thư <EMAIL> nhé! "}
        ],
        "utter_intent_hoi_the_visa_eco": [
            {"text": "Quý khách có thể phát hành thẻ Techcombank Visa Eco qua ứng dụng Techcombank Mobile, trực tiếp đến quầy giao dịch, hoặc liên hệ Tổng đài CSKH TCB. Đặc biệt, khi phát hành thẻ Techcombank Visa Eco lần đầu tiên và phát hành qua ứng dụng TCB Mobile, Quý khách sẽ được áp dụng Ưu đãi hoàn tiền lên tới 50% giá trị khi thanh toán mua vé di chuyển Tuyến Đường sắt số 1 HCM (Bến Thành - Suối Tiên). Chi tiết vui lòng tham khảo tại: https://techcombank.com/content/dam/techcombank/public-site/promo_file/vie-the-le-ctkm-hoan-phi-di-metro-hcm-debit-eco.pdf"}
        ],
        "utter_intent_hoi_chung_khoan_techcombank": [
            {"text": "Dạ Quý khách vui lòng liên hệ Fanpage https://www.facebook.com/techcomsecurities hoặc hotline 1800 588 826 để được hỗ trợ ạ"}
        ],
        "utter_intent_hoi_dich_vu_doanh_nghiep": [
            {"text": "Dạ để được tư vấn và hỗ trợ, bạn vui lòng liên hệ trung tâm Chăm sóc Khách hàng Doanh nghiệp 1800-6556 (trong nước) hoặc +84-24 7303 6556 (quốc tế), Hoặc liên hệ qua hòm thư: <EMAIL> nhé!"}
        ],
        "utter_intent_loi_ung_dung_app": [
            {"text": "Chào bạn,\nNgân hàng rất tiếc về những bất tiện mà bạn đã gặp phải.\nBạn vui lòng cập nhật phiên bản mới nhất của ứng dụng và thử lại nhé!"}
        ],
        "utter_intent_loi_tai_khoan_giao_dich": [
            {"text": "Dạ rất tiếc về vấn đề bạn đang gặp phải. Hiện tại, các thông tin liên quan đến tài khoản/giao dịch của Quý khách chỉ tổng đài CSKH và chi nhánh Ngân hàng có quyền truy cập để kiểm tra. Bộ phận Fanpage không thể kiểm tra được ạ. Quý khách vui lòng gọi vào tổng đài miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) để được hỗ trợ nhé! "}
        ],
        "utter_intent_yeu_cau_ho_tro_chung": [
            {"text": "Tôi có thể hỗ trợ bạn các thông tin về sản phẩm, dịch vụ của Techcombank, hướng dẫn thực hiện các giao dịch, giải đáp thắc mắc và nhiều hơn nữa. Bạn cần tôi giúp gì cụ thể ạ?"}
        ],
        "utter_intent_chuyen_tien_nham": [
            {"text": "Quý khách vui lòng gọi vào tổng đài miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) để được hỗ trợ kịp thời nhé!"}
        ],
        "utter_intent_hoi_so_hotline_ngan_hang": [
            {"text": "Quý khách vui lòng gọi vào tổng đài miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) để được hỗ trợ kịp thời nhé!"}
        ]
    }
}

intent_definitions = {
    "intent_mo_the_tin_dung": "Người dùng muốn tìm hiểu thông tin, điều kiện, thủ tục, hoặc cách thức để đăng ký và sở hữu một thẻ tín dụng (credit card). Họ có thể hỏi về các loại thẻ tín dụng, ưu đãi, phí, hoặc quy trình mở thẻ.",
    "intent_quen_mat_khau": "Người dùng không nhớ hoặc làm mất mật khẩu đăng nhập vào các dịch vụ của ngân hàng (thường là Internet Banking, Mobile Banking) và cần hướng dẫn cách khôi phục, lấy lại hoặc đặt lại mật khẩu mới.",
    "intent_vay_tieu_dung": "Người dùng có nhu cầu tìm hiểu thông tin, điều kiện, thủ tục, lãi suất liên quan đến các sản phẩm vay vốn cho mục đích tiêu dùng cá nhân, mua nhà, hoặc mua ô tô.",
    "intent_chi_nhanh_gan_nhat": "Người dùng muốn tìm kiếm địa điểm chi nhánh hoặc phòng giao dịch của ngân hàng gần vị trí hiện tại của họ hoặc một địa điểm cụ thể.",
    "intent_tuyen_dung": "Người dùng quan tâm đến các cơ hội việc làm, vị trí tuyển dụng, hoặc quy trình ứng tuyển tại ngân hàng.",
    "intent_khoa_the": "Người dùng muốn khóa thẻ (ATM, tín dụng, ghi nợ) của mình, thường do bị mất, thất lạc, nghi ngờ gian lận, hoặc thẻ bị nuốt vào máy ATM.",
    "intent_sinh_loi_tu_dong": "Người dùng hỏi về sản phẩm/dịch vụ cho phép số dư trong tài khoản thanh toán tự động sinh lãi suất (thường là lãi suất không kỳ hạn hoặc lãi suất ưu đãi qua đêm) mà không cần mở sổ tiết kiệm riêng.",
    "intent_rut_tien_sinh_loi_tu_dong": "Người dùng muốn biết cách thức hoặc thực hiện rút tiền từ phần lãi hoặc gốc của tài khoản có tính năng sinh lời tự động.",
    "intent_doi_diem": "Người dùng muốn tìm hiểu cách thức hoặc thực hiện việc quy đổi điểm thưởng tích lũy từ các giao dịch/chương trình khách hàng thân thiết thành quà tặng, voucher, dặm bay, hoặc các ưu đãi khác.",
    "intent_tinh_diem_giao_dich": "Người dùng muốn biết cách tính điểm thưởng cho các giao dịch, loại giao dịch nào được tích điểm, hoặc tỷ lệ quy đổi điểm.",
    "intent_han_muc_chuyen_tien": "Người dùng muốn biết giới hạn số tiền tối đa có thể chuyển trong một lần giao dịch, trong một ngày qua các kênh khác nhau (Internet Banking, Mobile Banking, ATM, tại quầy).",
    "intent_bieu_phi_the_&_sms_banking": "Người dùng muốn tìm hiểu về các loại phí liên quan đến việc sử dụng thẻ (ví dụ: phí thường niên, phí rút tiền, phí giao dịch ngoại tệ, phí chậm thanh toán thẻ tín dụng). Hoặc người dùng muốn biết về chi phí duy trì hoặc sử dụng dịch vụ thông báo biến động số dư qua tin nhắn SMS (SMS Banking).",
    "intent_cap_nhat_sdt_email": "Người dùng muốn thay đổi hoặc cập nhật thông tin cá nhân đã đăng ký với ngân hàng, cụ thể là số điện thoại hoặc địa chỉ email.",
    "intent_chon_stk": "Người dùng muốn lựa chọn một số tài khoản theo ý thích (số đẹp, dễ nhớ, theo ngày sinh,...) khi mở tài khoản mới.",
    "intent_giao_dich_khong_thanh_cong_&_loi_app": "Người dùng báo cáo hoặc hỏi về một giao dịch (chuyển tiền, thanh toán hóa đơn, rút tiền,...) không thực hiện được, bị lỗi, hoặc gặp sự cố trong quá trình thực hiện. Hoặc gặp sự cố hoặc báo lỗi khi sử dụng ứng dụng ngân hàng di động (Mobile Banking/Internet Banking trên app) như không đăng nhập được, app bị treo, tính năng không hoạt động.",
    "intent_dong_tai_khoan": "Người dùng muốn chấm dứt sử dụng và đóng vĩnh viễn tài khoản ngân hàng của họ.",
    "intent_lai_suat_tiet_kiem": "Người dùng muốn biết thông tin về lãi suất áp dụng cho các sản phẩm tiền gửi tiết kiệm có kỳ hạn hoặc không kỳ hạn.",
    "intent_lam_the_&_tai_khoan_online": "Người dùng muốn mở một tài khoản ngân hàng mới (thường là tài khoản thanh toán cơ bản). Hoặc người dùng muốn đăng ký mở thẻ (thường là thẻ ghi nợ hoặc thẻ tín dụng cơ bản) thông qua kênh trực tuyến của ngân hàng (website, app) mà không cần đến quầy.",
    "intent_phi_duy_tri_tai_khoan": "Người dùng muốn biết về các khoản phí phải trả để duy trì tài khoản thanh toán hoặc các loại tài khoản khác (phí thường niên tài khoản).",
    "intent_quen_ma_pin": "Người dùng không nhớ hoặc làm mất mã PIN (Personal Identification Number) của thẻ (ATM, tín dụng) và cần hướng dẫn cách khôi phục hoặc đặt lại mã PIN mới.",
    "intent_sao_ke_&_kiem_tra_so_du": "Người dùng muốn xem, yêu cầu, hoặc in bản kê chi tiết các giao dịch đã thực hiện trên tài khoản của họ trong một khoảng thời gian nhất định, đồng thời có thể bao gồm yêu cầu kiểm tra số dư tài khoản hiện tại hoặc tra cứu các khoản giao dịch cụ thể.",
    "intent_mo_tai_khoan_doanh_nghiep": "Người dùng (đại diện doanh nghiệp/tổ chức) muốn tìm hiểu thủ tục, điều kiện hoặc đăng ký mở tài khoản dành cho khách hàng doanh nghiệp, công ty.",
    "intent_loa_chuyen_tien": "Người dùng muốn tìm hiểu hoặc đăng ký dịch vụ lắp đặt thiết bị loa thông báo giao dịch thành công tại điểm bán hàng. Loa ting ting",
    "intent_lua_dao": "Người dùng nghi ngờ hoặc báo cáo về các hoạt động lừa đảo, gian lận liên quan đến tài khoản, thẻ, các giao dịch đáng ngờ, hoặc các hình thức lừa đảo qua mạng mạo danh ngân hàng.",
    "intent_thoi_gian_hoat_dong": "Người dùng muốn biết thời gian hoạt động, giờ mở/đóng cửa của các chi nhánh/phòng giao dịch ngân hàng, hoặc thời gian hỗ trợ của tổng đài.",
    "intent_dang_ky_tai_khoan_duoi_18": "Người dùng (hoặc người giám hộ) muốn đăng ký mở tài khoản ngân hàng cho người dưới 18 tuổi (bao gồm từ đủ 15 tuổi), qua eKYC trên ứng dụng di động. Có thể hỏi về điều kiện mở, giấy tờ cần thiết, quyền sử dụng và vai trò của người giám hộ.",
    "intent_hoi_soft_pos": "Người dùng muốn tìm hiểu thông tin về dịch vụ SoftPOS (Software Point of Sale) của Techcombank, một giải pháp cho phép biến điện thoại di động/máy tính bảng thành thiết bị chấp nhận thanh toán thẻ mà không cần máy POS truyền thống.",
    "intent_chaohoi": "Người dùng bắt đầu cuộc trò chuyện bằng những lời chào hỏi thông thường",
    "intent_thank_you": "Người dùng bày tỏ sự biết ơn, lời cảm ơn đối với sự hỗ trợ hoặc thông tin mà bot cung cấp. Người dùng bày tỏ cảm xúc tích cực, sự vui vẻ, hoặc hài lòng chung về dịch vụ, trải nghiệm hoặc sự hỗ trợ của bot. Người dùng thể hiện sự đồng tình, xác nhận một thông tin mà bot đưa ra, hoặc chấp thuận một đề xuất/hành động của bot.",
    "intent_mood_unhappy": "Người dùng bày tỏ cảm xúc tiêu cực, sự không vui, bực bội, hoặc không hài lòng một cách chung chung mà không chỉ rõ vấn đề cụ thể. Hoặc phàn nàn, khiếu nại về một vấn đề, dịch vụ, sản phẩm, nhân viên, hoặc trải nghiệm cụ thể không tốt mà họ gặp phải. Người dùng đưa ra nhận xét, góp ý chỉ ra điểm chưa tốt, cần khắc phục hoặc cải thiện ở một khía cạnh cụ thể. Người dùng phản hồi rằng thông tin hoặc câu trả lời mà bot cung cấp trong lượt tương tác trước đó là không chính xác, sai lệch hoặc hiểu sai ý của họ. Người dùng đưa ra nhận xét, góp ý mang tính xây dựng, khen ngợi một tính năng/dịch vụ cụ thể, hoặc đề xuất cải tiến theo hướng tích cực. Người dùng thể hiện sự không đồng tình, phủ nhận một thông tin, hoặc từ chối một đề xuất/hành động của bot. Người dùng bày tỏ sự bất bình cao độ, trực tiếp cáo buộc Techcombank hoặc các hoạt động của ngân hàng là lừa đảo, gian lận, hoặc cố ý gây thiệt hại cho họ. Đây là phàn nàn nghiêm trọng, khác với báo cáo lừa đảo từ bên thứ ba (intent_lua_dao)",
    "intent_ask_what_can_do": "Người dùng muốn biết về khả năng, phạm vi hỗ trợ, hoặc các chức năng mà bot/hệ thống có thể thực hiện.",
    "intent_out_of_scope": "Các câu hỏi, yêu cầu của người dùng không liên quan đến lĩnh vực hoạt động của ngân hàng, các sản phẩm/dịch vụ của ngân hàng, hoặc nằm ngoài khả năng xử lý, kiến thức đã được huấn luyện của bot.",
    "intent_dong_the_tin_dung": "Người dùng yêu cầu hủy hoặc đóng thẻ tín dụng hiện tại vì không còn nhu cầu sử dụng, muốn tránh phí duy trì, hoặc vì các lý do cá nhân khác. Có thể hỏi về quy trình đóng thẻ, các khoản phí liên quan, hoặc tình trạng nợ cần thanh toán trước khi đóng.",
    "intent_cung_cap_thong_tin_ca_nhan": "Người dùng chủ động cung cấp hoặc cung cấp theo yêu cầu các thông tin cá nhân như họ tên, số CMND/CCCD/Hộ chiếu, ngày sinh, số điện thoại, địa chỉ email, số tài khoản, số thẻ. Việc này thường nhằm mục đích xác thực danh tính, để nhân viên/hệ thống hỗ trợ kiểm tra thông tin, hoặc để hoàn tất một yêu cầu/giao dịch cụ thể mà trước đó họ đã đề cập.",
    "intent_cung_cap_ma_loi": "Người dùng cung cấp một mã lỗi cụ thể (ví dụ: 'ERR_TRAN_005', 'Mã lỗi 403', 'Lỗi không xác định X12') hoặc trích dẫn một thông báo lỗi hiển thị trên màn hình khi họ gặp sự cố với ứng dụng ngân hàng, website, ATM, hoặc trong quá trình thực hiện giao dịch.",
    "intent_hoi_thong_tin_khac": "Người dùng đưa ra câu hỏi hoặc yêu cầu thông tin liên quan đến các sản phẩm, dịch vụ, chính sách, quy định, hoặc các hoạt động chung của ngân hàng, nhưng nội dung này chưa (hoặc không thể) được phân loại vào một trong các intent chuyên biệt đã được định nghĩa trước đó. Câu hỏi vẫn nằm trong phạm vi nghiệp vụ ngân hàng và không phải là intent_out_of_scope.",
    "mo_the_tin_dung": "Người dùng muốn tìm hiểu thông tin, điều kiện, thủ tục, hoặc cách thức để đăng ký và sở hữu một thẻ tín dụng (credit card). Họ có thể hỏi về các loại thẻ tín dụng, ưu đãi, phí, hoặc quy trình mở thẻ.",
    "quen_mat_khau": "Người dùng không nhớ hoặc làm mất mật khẩu đăng nhập vào các dịch vụ của ngân hàng (thường là Internet Banking, Mobile Banking) và cần hướng dẫn cách khôi phục, lấy lại hoặc đặt lại mật khẩu mới.",
    "vay_tieu_dung": "Người dùng có nhu cầu tìm hiểu thông tin, điều kiện, thủ tục, lãi suất liên quan đến các sản phẩm vay vốn cho mục đích tiêu dùng cá nhân, mua nhà, hoặc mua ô tô.",
    "chi_nhanh_gan_nhat": "Người dùng muốn tìm kiếm địa điểm chi nhánh hoặc phòng giao dịch của ngân hàng gần vị trí hiện tại của họ hoặc một địa điểm cụ thể.",
    "tuyen_dung": "Người dùng quan tâm đến các cơ hội việc làm, vị trí tuyển dụng, hoặc quy trình ứng tuyển tại ngân hàng.",
    "khoa_the": "Người dùng muốn khóa thẻ (ATM, tín dụng, ghi nợ) của mình, thường do bị mất, thất lạc, nghi ngờ gian lận, hoặc thẻ bị nuốt vào máy ATM.",
    "sinh_loi_tu_dong": "Người dùng hỏi về sản phẩm/dịch vụ cho phép số dư trong tài khoản thanh toán tự động sinh lãi suất (thường là lãi suất không kỳ hạn hoặc lãi suất ưu đãi qua đêm) mà không cần mở sổ tiết kiệm riêng.",
    "rut_tien_sinh_loi_tu_dong": "Người dùng muốn biết cách thức hoặc thực hiện rút tiền từ phần lãi hoặc gốc của tài khoản có tính năng sinh lời tự động.",
    "doi_diem": "Người dùng muốn tìm hiểu cách thức hoặc thực hiện việc quy đổi điểm thưởng tích lũy từ các giao dịch/chương trình khách hàng thân thiết thành quà tặng, voucher, dặm bay, hoặc các ưu đãi khác.",
    "tinh_diem_giao_dich": "Người dùng muốn biết cách tính điểm thưởng cho các giao dịch, loại giao dịch nào được tích điểm, hoặc tỷ lệ quy đổi điểm.",
    "han_muc_chuyen_tien": "Người dùng muốn biết giới hạn số tiền tối đa có thể chuyển trong một lần giao dịch, trong một ngày qua các kênh khác nhau (Internet Banking, Mobile Banking, ATM, tại quầy).",
    "bieu_phi_the_&_sms_banking": "Người dùng muốn tìm hiểu về các loại phí liên quan đến việc sử dụng thẻ (ví dụ: phí thường niên, phí rút tiền, phí giao dịch ngoại tệ, phí chậm thanh toán thẻ tín dụng). Hoặc người dùng muốn biết về chi phí duy trì hoặc sử dụng dịch vụ thông báo biến động số dư qua tin nhắn SMS (SMS Banking).",
    "cap_nhat_sdt_email": "Người dùng muốn thay đổi hoặc cập nhật thông tin cá nhân đã đăng ký với ngân hàng, cụ thể là số điện thoại hoặc địa chỉ email.",
    "chon_stk": "Người dùng muốn lựa chọn một số tài khoản theo ý thích (số đẹp, dễ nhớ, theo ngày sinh,...) khi mở tài khoản mới.",
    "giao_dich_khong_thanh_cong_&_loi_app": "Người dùng báo cáo hoặc hỏi về một giao dịch (chuyển tiền, thanh toán hóa đơn, rút tiền,...) không thực hiện được, bị lỗi, hoặc gặp sự cố trong quá trình thực hiện. Hoặc gặp sự cố hoặc báo lỗi khi sử dụng ứng dụng ngân hàng di động (Mobile Banking/Internet Banking trên app) như không đăng nhập được, app bị treo, tính năng không hoạt động.",
    "dong_tai_khoan": "Người dùng muốn chấm dứt sử dụng và đóng vĩnh viễn tài khoản ngân hàng của họ.",
    "lai_suat_tiet_kiem": "Người dùng muốn biết thông tin về lãi suất áp dụng cho các sản phẩm tiền gửi tiết kiệm có kỳ hạn hoặc không kỳ hạn.",
    "lam_the_&_tai_khoan_online": "Người dùng muốn mở một tài khoản ngân hàng mới (thường là tài khoản thanh toán cơ bản). Hoặc người dùng muốn đăng ký mở thẻ (thường là thẻ ghi nợ hoặc thẻ tín dụng cơ bản) thông qua kênh trực tuyến của ngân hàng (website, app) mà không cần đến quầy.",
    "phi_duy_tri_tai_khoan": "Người dùng muốn biết về các khoản phí phải trả để duy trì tài khoản thanh toán hoặc các loại tài khoản khác (phí thường niên tài khoản).",
    "quen_ma_pin": "Người dùng không nhớ hoặc làm mất mã PIN (Personal Identification Number) của thẻ (ATM, tín dụng) và cần hướng dẫn cách khôi phục hoặc đặt lại mã PIN mới.",
    "sao_ke_&_kiem_tra_so_du": "Người dùng muốn xem, yêu cầu, hoặc in bản kê chi tiết các giao dịch đã thực hiện trên tài khoản của họ trong một khoảng thời gian nhất định, đồng thời có thể bao gồm yêu cầu kiểm tra số dư tài khoản hiện tại hoặc tra cứu các khoản giao dịch cụ thể.",
    "mo_tai_khoan_doanh_nghiep": "Người dùng (đại diện doanh nghiệp/tổ chức) muốn tìm hiểu thủ tục, điều kiện hoặc đăng ký mở tài khoản dành cho khách hàng doanh nghiệp, công ty.",
    "loa_chuyen_tien": "Người dùng (thường là chủ cửa hàng/doanh nghiệp nhỏ) muốn tìm hiểu hoặc đăng ký dịch vụ lắp đặt thiết bị loa phát ra âm thanh thông báo giao dịch thành công tại điểm bán hàng (thường liên quan đến thanh toán QR hoặc POS).",
    "lua_dao": "Người dùng nghi ngờ hoặc báo cáo về các hoạt động lừa đảo, gian lận liên quan đến tài khoản, thẻ, các giao dịch đáng ngờ, hoặc các hình thức lừa đảo qua mạng mạo danh ngân hàng.",
    "thoi_gian_hoat_dong": "Người dùng muốn biết thời gian hoạt động, giờ mở/đóng cửa của các chi nhánh/phòng giao dịch ngân hàng, hoặc thời gian hỗ trợ của tổng đài.",
    "dang_ky_tai_khoan_duoi_18": "Người dùng (hoặc người giám hộ) muốn đăng ký mở tài khoản ngân hàng cho người dưới 18 tuổi (bao gồm từ đủ 15 tuổi), qua eKYC trên ứng dụng di động. Có thể hỏi về điều kiện mở, giấy tờ cần thiết, quyền sử dụng và vai trò của người giám hộ.",
    "hoi_soft_pos": "Người dùng muốn tìm hiểu thông tin về dịch vụ SoftPOS (Software Point of Sale) của Techcombank, một giải pháp cho phép biến điện thoại di động/máy tính bảng thành thiết bị chấp nhận thanh toán thẻ mà không cần máy POS truyền thống.",
    "chaohoi": "Người dùng bắt đầu cuộc trò chuyện bằng những lời chào hỏi thông thường, không kèm theo yêu cầu cụ thể nào.",
    "thank_you": "Người dùng bày tỏ sự biết ơn, lời cảm ơn đối với sự hỗ trợ hoặc thông tin mà bot cung cấp. Người dùng bày tỏ cảm xúc tích cực, sự vui vẻ, hoặc hài lòng chung về dịch vụ, trải nghiệm hoặc sự hỗ trợ của bot. Người dùng thể hiện sự đồng tình, xác nhận một thông tin mà bot đưa ra, hoặc chấp thuận một đề xuất/hành động của bot.",
    "mood_unhappy": "Người dùng bày tỏ cảm xúc tiêu cực, sự không vui, bực bội, hoặc không hài lòng một cách chung chung mà không chỉ rõ vấn đề cụ thể. Hoặc phàn nàn, khiếu nại về một vấn đề, dịch vụ, sản phẩm, nhân viên, hoặc trải nghiệm cụ thể không tốt mà họ gặp phải. Người dùng đưa ra nhận xét, góp ý chỉ ra điểm chưa tốt, cần khắc phục hoặc cải thiện ở một khía cạnh cụ thể. Người dùng phản hồi rằng thông tin hoặc câu trả lời mà bot cung cấp trong lượt tương tác trước đó là không chính xác, sai lệch hoặc hiểu sai ý của họ. Người dùng đưa ra nhận xét, góp ý mang tính xây dựng, khen ngợi một tính năng/dịch vụ cụ thể, hoặc đề xuất cải tiến theo hướng tích cực. Người dùng thể hiện sự không đồng tình, phủ nhận một thông tin, hoặc từ chối một đề xuất/hành động của bot. Người dùng bày tỏ sự bất bình cao độ, trực tiếp cáo buộc Techcombank hoặc các hoạt động của ngân hàng là lừa đảo, gian lận, hoặc cố ý gây thiệt hại cho họ. Đây là phàn nàn nghiêm trọng, khác với báo cáo lừa đảo từ bên thứ ba (intent_lua_dao)",
    "ask_what_can_do": "Người dùng muốn biết về khả năng, phạm vi hỗ trợ, hoặc các chức năng mà bot/hệ thống có thể thực hiện.",
    "out_of_scope": "Các câu hỏi, yêu cầu của người dùng không liên quan đến lĩnh vực hoạt động của ngân hàng, các sản phẩm/dịch vụ của ngân hàng, hoặc nằm ngoài khả năng xử lý, kiến thức đã được huấn luyện của bot.",
    "dong_the_tin_dung": "Người dùng yêu cầu hủy hoặc đóng thẻ tín dụng hiện tại vì không còn nhu cầu sử dụng, muốn tránh phí duy trì, hoặc vì các lý do cá nhân khác. Có thể hỏi về quy trình đóng thẻ, các khoản phí liên quan, hoặc tình trạng nợ cần thanh toán trước khi đóng.",
    "cung_cap_thong_tin_ca_nhan": "Người dùng chủ động cung cấp hoặc cung cấp theo yêu cầu các thông tin cá nhân như họ tên, số CMND/CCCD/Hộ chiếu, ngày sinh, số điện thoại, địa chỉ email, số tài khoản, số thẻ. Việc này thường nhằm mục đích xác thực danh tính, để nhân viên/hệ thống hỗ trợ kiểm tra thông tin, hoặc để hoàn tất một yêu cầu/giao dịch cụ thể mà trước đó họ đã đề cập.",
    "cung_cap_ma_loi": "Người dùng cung cấp một mã lỗi cụ thể (ví dụ: 'ERR_TRAN_005', 'Mã lỗi 403', 'Lỗi không xác định X12') hoặc trích dẫn một thông báo lỗi hiển thị trên màn hình khi họ gặp sự cố với ứng dụng ngân hàng, website, ATM, hoặc trong quá trình thực hiện giao dịch.",
    "hoi_thong_tin_khac": "Người dùng đưa ra câu hỏi hoặc yêu cầu thông tin liên quan đến các sản phẩm, dịch vụ, chính sách, quy định, hoặc các hoạt động chung của ngân hàng, nhưng nội dung này chưa (hoặc không thể) được phân loại vào một trong các intent chuyên biệt đã được định nghĩa trước đó. Câu hỏi vẫn nằm trong phạm vi nghiệp vụ ngân hàng và không phải là intent_out_of_scope.",
    "intent_doi_the_tu_sang_chip": "Người dùng có nhu cầu tìm hiểu, yêu cầu hoặc đăng ký chuyển đổi thẻ ATM từ loại thẻ từ sang thẻ chip. Người dùng có thể hỏi về lý do nên đổi thẻ, lợi ích của thẻ chip (bảo mật cao hơn, đáp ứng quy định của ngân hàng hoặc nhà nước), quy trình đổi thẻ, giấy tờ/thủ tục cần thiết, địa điểm đổi thẻ, chi phí, thời gian nhận thẻ mới, hoặc các chính sách hỗ trợ từ ngân hàng dành cho việc chuyển đổi này.",
    "intent_mo_tai_khoan_tiet_kiem_online": "Người dùng muốn tìm hiểu thông tin, điều kiện, thủ tục hoặc đăng ký mở tài khoản tiết kiệm trực tuyến thông qua các kênh online của ngân hàng (website, ứng dụng di động). Người dùng có thể hỏi về quy trình mở tài khoản, các loại sản phẩm tiết kiệm online, lãi suất, ưu đãi, kỳ hạn, điều kiện rút trước hạn và các tiện ích liên quan.",
    "intent_thoi_gian_nhan_the": "Người dùng muốn biết sau khi đăng ký/mở thẻ (ATM, ghi nợ, tín dụng), sẽ mất bao lâu để nhận được thẻ. Người dùng có thể hỏi về thời gian phát hành, thời gian giao nhận tại chi nhánh, hoặc thời gian nhận thẻ qua bưu điện, cũng như các yếu tố có thể ảnh hưởng đến tiến độ phát hành thẻ.",
    "intent_gia_han_the": "Người dùng muốn tìm hiểu thông tin, điều kiện hoặc quy trình gia hạn thẻ ngân hàng (ATM, ghi nợ, tín dụng) khi thẻ sắp hết hạn hoặc đã hết hạn. Người dùng có thể hỏi về thủ tục gia hạn, lệ phí, thời gian nhận thẻ mới, yêu cầu xác minh thông tin hoặc các hỗ trợ liên quan đến việc tiếp tục sử dụng thẻ sau khi hết hạn.",
    "intent_hoi_the_visa_eco": "Người dùng muốn tìm hiểu về thẻ Techcombank Visa Eco, bao gồm đặc điểm, quyền lợi, ưu đãi, điều kiện và thủ tục mở thẻ, biểu phí, hạn mức sử dụng, hướng dẫn đăng ký, cũng như các chương trình khuyến mãi hoặc hỗ trợ liên quan đến loại thẻ này.",
    "intent_hoi_chung_khoan_techcombank": "Người dùng muốn tìm hiểu thông tin liên quan đến các dịch vụ, sản phẩm, tài khoản chứng khoán của Techcombank hoặc Techcom Securities. Người dùng có thể hỏi về quy trình mở tài khoản chứng khoán, các loại sản phẩm đầu tư, biểu phí, ưu đãi, điều kiện giao dịch, cách nạp/rút tiền, hoặc các chương trình hỗ trợ khách hàng về chứng khoán tại Techcombank/Techcom Securities.",
    "intent_hoi_dich_vu_doanh_nghiep": "Người dùng muốn tìm hiểu thông tin, điều kiện, thủ tục hoặc đăng ký các sản phẩm, dịch vụ dành cho doanh nghiệp như vay vốn doanh nghiệp, mở rộng kinh doanh, tài trợ vốn lưu động, mở tài khoản công ty, hoặc các chính sách, ưu đãi, giải pháp tài chính liên quan đến hoạt động của doanh nghiệp tại ngân hàng.",
    "intent_loi_ung_dung_app": "Người dùng gặp sự cố khi sử dụng ứng dụng ngân hàng như bị văng ra khỏi app, app tự động thoát, không đăng nhập được, ứng dụng bị treo, đơ, hoặc các lỗi kỹ thuật khác làm gián đoạn trải nghiệm sử dụng dịch vụ trên app.",
    "intent_loi_tai_khoan_giao_dich": "Người dùng gặp sự cố liên quan đến tài khoản hoặc giao dịch như: bị trừ tiền không rõ lý do, không nhận được tiền chuyển vào tài khoản, số dư không chính xác, phát sinh giao dịch lạ, hoặc các vấn đề khác ảnh hưởng đến số dư và lịch sử giao dịch trên tài khoản ngân hàng.",
    "intent_yeu_cau_ho_tro_chung": "Người dùng bày tỏ nhu cầu cần được hỗ trợ, tư vấn hoặc trợ giúp từ ngân hàng nhưng chưa nêu rõ nội dung, vấn đề hoặc dịch vụ cụ thể. Đây thường là các lời đề nghị hỗ trợ chung, mở đầu cho cuộc hội thoại hoặc khi người dùng chưa xác định rõ yêu cầu.",
    "intent_chuyen_tien_nham": "Người dùng gặp sự cố chuyển tiền nhầm, ví dụ chuyển sai số tài khoản, chuyển nhầm người nhận, hoặc nhập sai thông tin khi giao dịch. Người dùng muốn được hỗ trợ tra soát, thu hồi giao dịch, hoặc hỏi về quy trình giải quyết khi chuyển khoản nhầm tại ngân hàng.",
    "intent_hoi_so_hotline_ngan_hang": "Người dùng hỏi về số hotline, số điện thoại tổng đài hoặc kênh liên hệ chính thức của ngân hàng để được tư vấn, hỗ trợ hoặc giải đáp thắc mắc."
}